import { <PERSON>, Button, Container } from "@mui/material";
import React, { useEffect, useState } from "react";
import { useParams } from "react-router-dom";
import BackButton from "../../components/common/BackButton";

import { Client } from "../../api/client";

import UseGlobalContext from "../../lib/hooks/UseGlobalContext";

import LeaderBoardUi from "../../components/common/LeaderBoardUi";
const DynamicPopup = React.lazy(() =>
  import("../../components/common/DynamicPopup")
);

const LeaderBoard = () => {
  const { title: id } = useParams();
  const [popupOpen, setPopupOpen] = useState(false);
  const [tournamentDetails, setTournamentDetails] = useState([]);
  const [loading, setLoading] = useState(false);

  const { user } = UseGlobalContext();
  useEffect(() => {
    const fetchTournamentDetails = async () => {
      setLoading(true);
      try {
        const response = await Client.get(`/tournament/${id}`);
        if (response.data.success) {
          setTournamentDetails(response.data.data);
        } else {
          console.error(response.data.error.message || "something went wrong");
        }
      } catch (error) {
        console.error("Error fetching tournament details:", error);

        // navigate("/tournaments");
      } finally {
        setLoading(false);
      }
    };
    fetchTournamentDetails();
  }, [id]);

  return (
    <Container maxWidth="xl" sx={{ py: 4, pt: 2, minHeight: "70dvh" }}>
      <Box
        sx={{
          display: "flex",
          flexDirection: "row",
          justifyContent: "space-between",
          alignItems: "center",
        }}
      >
        <BackButton />
        {user?.role === "arbiter" && (
          <Button
            size="small"
            variant="contained"
            onClick={() => setPopupOpen(true)}
          >
            Upload
          </Button>
        )}
      </Box>
      <LeaderBoardUi
        loading={loading}
        setLoading={setLoading}
        tournamentDetails={tournamentDetails}
      />
      <DynamicPopup
        open={popupOpen}
        onClose={() => setPopupOpen(false)}
        title="Upload Pairing Details"
        file_title="Ranking Details"
        tournament={tournamentDetails}
        type={1}
        // content={<UploadPairingDetails tournamentId={id} round={round}
      />
    </Container>
  );
};

export default LeaderBoard;
