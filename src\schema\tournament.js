import { z } from "zod";

const phoneRegex = /^(91)?\d{10}$/;

// Create a function that generates the schema based on whether it's for editing or creating
export const createTournamentSchema = (isEditing = false) =>
  z
    .object({
      // Tournament Details
      title: z.string().min(1, "Tournament title is required"),
      fideRated: z.coerce.boolean().default(false),
      organizerName: z.string().min(1, "Organizer name is required"),
      tournamentLevel: z.enum(["state", "national", "district", "global"], {
        errorMap: () => ({ message: "Please select a tournament level" }),
      }),

      // Tournament Dates
      startDate: z
        .string()
        .regex(/^\d{4}-\d{2}-\d{2}$/, "Please Enter the Start Date")
        .refine(
          (date) => isEditing || new Date(date) >= new Date(),
          isEditing ? true : "Start date must be in the future"
        ),
      endDate: z
        .string()
        .regex(/^\d{4}-\d{2}-\d{2}$/, "Please Enter the End Date")
        .refine(
          (date) => isEditing || new Date(date) >= new Date(),
          isEditing ? true : "End date must be in the future"
        )
        .refine((date, ctx) => {
          if (!ctx?.parent?.startDate) return true;
          return new Date(date) >= new Date(ctx.parent.startDate);
        }, "End date must be after start date"),
      reportingTime: z
        .string()
        .regex(
          /^(0[1-9]|1[0-2]):([0-5][0-9])\s(AM|PM)$/i,
          "Please enter a Time"
        )
        .transform((time) => {
          // Normalize time format
          const match = time.match(/^(0[1-9]|1[0-2]):([0-5][0-9])\s(AM|PM)$/i);
          if (!match) return time;
          const [, hour, minute, period] = match;
          return `${hour}:${minute} ${period.toUpperCase()}`;
        }),
      // Makes the field optional (null or undefined are allowed)
      // Age Categories
      tournamentCategory: z
        .enum(["open", "male", "female"], {
          errorMap: () => ({ message: "Please select a tournament category" }),
        })
        .default("open"),
      maleAgeCategory: z.array(
        z.string({
          errorMap: () => ({ message: "Please select a valid age category" }),
        })
      ),
      femaleAgeCategory: z.array(
        z.string({
          errorMap: () => ({ message: "Please select a valid age category" }),
        })
      ),

      // Registration
      registrationStartDate: z
        .string()
        .regex(/^\d{4}-\d{2}-\d{2}$/, "Please Enter the Start Date")
        .refine(
          (date) => isEditing || new Date(date) >= new Date(),
          isEditing ? true : "Registration start date must be in the future"
        )
        .refine((date, ctx) => {
          if (!ctx?.parent?.startDate) return true;
          return new Date(date) <= new Date(ctx.parent.startDate);
        }, "Registration start date must not be after tournament end date"),
      registrationEndDate: z
        .string()
        .regex(/^\d{4}-\d{2}-\d{2}$/, "Please Enter the End Date")
        .refine(
          (date) => isEditing || new Date(date) >= new Date(),
          isEditing ? true : "Registration end date must be in the future"
        )
        .refine((date, ctx) => {
          if (!ctx?.parent?.endDate) return true;
          if (!ctx?.parent?.registrationStartDate) return true;
          if (!ctx?.parent?.startDate) return true;
          return (
            new Date(date) <= new Date(ctx.parent.endDate) &&
            new Date(date) >= new Date(ctx.parent.registrationStartDate) &&
            new Date(date) <= new Date(ctx.parent.startDate)
          );
        }, "Registration end date must be between registration start date and tournament start date"),
      registrationEndTime: z
        .string() // Trim whitespace
        .regex(
          /^(0[1-9]|1[0-2]):([0-5][0-9])\s?(AM|PM)$/i,
          "Please enter a Time"
        )
        .transform((time) => {
          const match = time.match(/^(0[1-9]|1[0-2]):([0-5][0-9])\s?(AM|PM)$/i);
          if (!match) return time;
          const [, hour, minute, period] = match;
          return `${hour}:${minute} ${period.toUpperCase()}`;
        }),
      // Tournament Officials
      chiefArbiterName: z
        .object({
          name: z.string().nullable().optional(),
          id: z.string().nullable().optional(),
        })
        .nullable()
        .transform((data) => data || { name: null, id: null })
        .refine(
          (data) => {
            // If required: both name and id must be present
            // Otherwise: allow both to be null/empty or both to be filled
            return (!data.name && !data.id) || (data.name && data.id);
          },
          {
            message: "Please Select the arbiter",
            path: ["name"],
          }
        ),

      tournamentDirectorName: z
        .string()
        .min(1, "Tournament director name is required"),
      entryFeeCurrency: z.enum(["INR", "USD", "EUR"]).default("INR"),
      entryFee: z.coerce.number().min(1, "Registration fee is required"),
      numberOfRounds: z.coerce
        .number({
          errorMap: () => ({ message: "Please enter number of rounds" }),
        })
        .int()
        .positive(),
      timeControl: z.enum(["classical", "rapid", "bullet", "blitz"], {
        errorMap: () => ({ message: "Please select a time control" }),
      }),
      timeControlDuration: z.coerce.string().min(1, "Duration must be given"),
      timeControlIncrement: z.coerce.string().min(1, "Increment must be given"),
      tournamentType: z
        .enum(["individual", "team"], {
          errorMap: () => ({ message: "Please select a tournament type" }),
        })
        .default("individual"),
      tournamentSystem: z
        .enum(["swiss-system", "round-robin", "knockout"])
        .default("swiss-system"),
      nationalApproval: z.string().optional(),
      stateApproval: z.string().optional(),
      districtApproval: z.string().optional(),
      fideApproval: z.string().optional(),
      contactPersonName: z.string().min(1, "Contact person name is required"),
      email: z.string().email("Enter Proper email address"),
      contactNumber: z
        .string({ errorMap: () => ({ message: "Contact number is required" }) })
        .regex(phoneRegex, "Enter a phone number"),

      alternateContactNumber: z
        .string({
          errorMap: () => ({
            message: " alternate Contact number is required",
          }),
        })
        .regex(phoneRegex, "Enter a  phone number"),
      numberOfTrophiesMale: z.coerce
        .number({
          errorMap: () => ({ message: "Number of trophies is required" }),
        })
        .int()
        .min(1, "Please enter a number of trophies for male")
        .max(1000),
      numberOfTrophiesFemale: z.coerce
        .number({
          errorMap: () => ({ message: "Number of trophies is required" }),
        })
        .int()
        .min(1, "Please enter a number of trophies for female")
        .max(1000),

      totalCashPrizeCurrency: z.enum(["INR", "USD", "EUR"]).default("INR"),
      totalCashPrizeAmount: z.coerce
        .number({
          errorMap: () => ({ message: "Total cash prize amount is required" }),
        })
        .min(1, "Total cash prize amount required")
        .max(1000000),
      country: z.string().min(1, "Country is required"),
      state: z.string().min(2, "State name must be at least 2 characters"),
      district: z
        .string()
        .min(2, "District name must be at least 2 characters"),
      city: z.string().min(2, "City name must be at least 2 characters"),
      pincode: z.string().regex(/^\d{6}$/, "Pincode must be exactly 6 digits"),
      chatUrl: z.string().nullish(),
      venueAddress: z
        .string()
        .min(10, "Venue address must be at least 10 characters long")
        .max(200, "Venue address cannot exceed 200 characters"),
      nearestLandmark: z
        .string()
        .min(5, "Landmark must be at least 5 characters")
        .max(100, "Landmark cannot exceed 100 characters"),
      // brochureUrl: z.string().url("Invalid brochure URL format").nullish(),
      locationUrl: z
        .string({ errorMap: () => ({ message: "Location URL is required" }) })
        .url("Location URL is Required"),
      chessboardProvided: z.coerce.boolean().default(false),
      timerProvided: z.coerce.boolean().default(false),
      spotEntry: z.coerce.boolean().default(false),
      parkingFacility: z.enum(["yes", "no", "limited"]).default("no"),
      foodFacility: z
        .array(
          z.enum(["breakfast", "lunch", "dinner", "snacks", "beverages", "nil"])
        )
        .optional(),
      brochure: z
        .any()
        .optional()
        .refine(
          (file) => {
            if (!file || file === "") return true; // Skip validation if no file
            return file instanceof File;
          },
          { message: "Please upload a valid file" }
        )
        .refine(
          (file) => {
            if (!file || file === "") return true; // Skip validation if no file
            return file.size <= 5 * 1024 * 1024; // 5MB limit
          },
          { message: "File must be less than 5MB" }
        )
        .refine(
          (file) => {
            if (!file || file === "") return true; // Skip validation if no file
            return [
              "image/jpeg",
              "image/png",
              "image/jpg",
              "image/webp",
              "application/pdf",
              "application/msword",
              "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
              "application/vnd.openxmlformats-officedocument.presentationml.presentation",
            ].includes(file.type);
          },
          { message: "File must be PDF/Word/PPT, or JPEG, PNG, or WebP format" } // { message: "File must be JPEG, PNG, or WebP format" }
        ),
      brochureFilePreview: z.string().optional().nullable(),
    })
    .superRefine(
      (
        {
          contactNumber,
          alternateContactNumber,
          tournamentCategory,
          femaleAgeCategory,
          maleAgeCategory,
          tournamentLevel,
          nationalApproval,
          stateApproval,
          districtApproval,
          fideApproval,
        },
        ctx
      ) => {
        if (contactNumber === alternateContactNumber) {
          ctx.addIssue({
            path: ["alternateContactNumber"],
            message:
              "Alternate contact number must be different from the primary contact number",
          });
        }
        if (tournamentCategory === "open") {
          if (maleAgeCategory.length === 0) {
            ctx.addIssue({
              path: ["maleAgeCategory"],
              message: "Male age category required for open tournaments",
            });
          }
          if (femaleAgeCategory.length === 0) {
            ctx.addIssue({
              path: ["femaleAgeCategory"],
              message: "Female age category required for open tournaments",
            });
          }
        }
        if (tournamentCategory === "female" && femaleAgeCategory.length === 0) {
          ctx.addIssue({
            path: ["femaleAgeCategory"],
            message: "Female age category required",
          });
        }
        if (tournamentCategory === "male" && maleAgeCategory.length === 0) {
          ctx.addIssue({
            path: ["maleAgeCategory"],
            message: "Male age category required",
          });
        }
        if (tournamentLevel === "national" && !nationalApproval) {
          ctx.addIssue({
            path: ["nationalApproval"],
            message: "National approval required for national tournaments",
          });
        }
        if (tournamentLevel === "state" && !stateApproval) {
          ctx.addIssue({
            path: ["stateApproval"],
            message: "State approval required for state tournaments",
          });
        }
        if (tournamentLevel === "district" && !districtApproval) {
          ctx.addIssue({
            path: ["districtApproval"],
            message: "District approval required for district tournaments",
          });
        }
        if (tournamentLevel === "global" && !fideApproval) {
          ctx.addIssue({
            path: ["fideApproval"],
            message: "FIDE approval required for global tournaments",
          });
        }
      }
    );

// For creating new tournaments
export const tournamentSchema = createTournamentSchema(false);

// For editing existing tournaments
export const tournamentEditSchema = createTournamentSchema(true);
