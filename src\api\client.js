import axios from "axios";

const backend_baseURL = import.meta.env.VITE_API_URL;

export const Client = axios.create({
  baseURL: backend_baseURL,
  headers: {
    "Content-Type": "application/json",
  },
  withCredentials: true,
});

// Token management without hooks
let currentToken = localStorage.getItem("jwt_token") || null;
let refreshAttempts = 0;
const MAX_REFRESH_ATTEMPTS = 3;

// Initialize token from localStorage on module load
if (currentToken) {
  Client.defaults.headers.common["Authorization"] = `Bearer ${currentToken}`;
}

export const updateToken = (token) => {
  currentToken = token;
  if (token) {
    localStorage.setItem("jwt_token", token);
    Client.defaults.headers.common["Authorization"] = `Bearer ${token}`;
  } else {
    localStorage.removeItem("jwt_token");
    delete Client.defaults.headers.common["Authorization"];
  }
};

// Set up request interceptor
Client.interceptors.request.use((config) => {
  // Always check localStorage to ensure we have the latest token
  // This is important for the initial page load and cross-tab communication
  const token = localStorage.getItem("jwt_token") || currentToken;
  if (token) {
    config.headers.Authorization = `Bearer ${token}`;
    // Keep currentToken in sync
    if (token !== currentToken) {
      currentToken = token;
    }
  }
  return config;
});

// Set up response interceptor
Client.interceptors.response.use(
  (response) => response,
  async (error) => {
    const originalRequest = error.config;
if (
      error.response &&
      error.response.status === 401 &&
      error.response.data?.message === "Token has expired" ||
      error.response.data?.message === "No authentication token, access denied" &&
      !originalRequest._retry &&
      refreshAttempts < MAX_REFRESH_ATTEMPTS
    ) {
      originalRequest._retry = true;
      refreshAttempts++;

      try {
        const newToken = await refreshToken();
if (newToken) {
          updateToken(newToken);
          if (originalRequest.headers) {
            originalRequest.headers.Authorization = `Bearer ${newToken}`;
          }
          return Client(originalRequest);
        }
      } catch (refreshError) {
        updateToken(null);
        return Promise.reject(refreshError);
      }
    } else if (
      (error.response &&
        error.response.status === 401 &&
        error.response.data?.message ===
          "No authentication token, access denied") ||
      error.response.data?.message === "Invalid token"
    ) {
      // Instead of calling logoutUser directly, we'll dispatch an event
      // Dispatch a logout event
      window.dispatchEvent(new CustomEvent("logout"));

      // Clear the token and user data
      updateToken(null);
      localStorage.removeItem("user");

      // Don't force page reload - let the React Router handle navigation
      // This prevents the 404 page flash during auth refresh
      // window.location.reload();

      return Promise.reject(error);
    }

    if (refreshAttempts >= MAX_REFRESH_ATTEMPTS) {
      refreshAttempts = 0;
      updateToken(null);
    }

    return Promise.reject(error);
  }
);

// Refresh token function
export const refreshToken = async () => {
  try {
    const response = await Client.get("/auth/refresh", {
      withCredentials: true,
    });
    if (!response.data.success) return null;
    const { accessToken } = response.data.data;
    return accessToken;
  } catch (error) {
    console.error("Error refreshing token:", error);
    return null;
  }
};
