import React, { useState, useEffect } from "react";
import {
  Avatar,
  Box,
  Button,
  Container,
  IconButton,
  Paper,
  Skeleton,
  Stack,
  Tooltip,
  Typography,
} from "@mui/material";
import { <PERSON><PERSON>, <PERSON>, PinDrop } from "@mui/icons-material";
import { Client } from "../../api/client";
import UseToast from "../../lib/hooks/UseToast";
import { useNavigate } from "react-router-dom";
import { DetailTable } from "../../components/common/DetailTable";
import BackButton from "../../components/common/BackButton";

/**
 * Format club data for display in the detail table
 * @param {Object} club - Club data from API
 * @returns {Object} Formatted club data with title and details
 */
function formatClubData(club) {
  if (!club) {
    return {
      title: "",
      details: [],
    };
  }

  // Create a list of details to display
  const details = [
    {
      label: "Club Name",
      value: club.clubName || "-",
    },
    {
      label: "Club ID",
      value: club.clubDistrictId || "-",
    },

    {
      label: "Contact Person",
      value: club.contactPersonName || "-",
    },
    {
      label: "Contact Person Phone Number",
      value: club.contactPersonNumber || "-",
    },
    {
      label: "Alternate Contact Number",
      value: club.alternateContactNumber || "-",
    },
    {
      label: "Contact Person Email",
      value: club.contactPersonEmail || "-",
    },
    {
      label: "Country",
      value: club.country || "-",
    },
    {
      label: "State",
      value: club.state || "-",
    },
    {
      label: "District",
      value: club.district || "-",
    },
    {
      label: "City",
      value: club.city || "-",
    },

    {
      label: "Address",
      value: club.address || "-",
    },
  ];

  return {
    title: club.clubName || "",
    details: details,
  };
}

const ClubProfilePage = () => {
  const [clubInfo, setClubInfo] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  // const { user } = useGlobalContext();
  const toast = UseToast();
  const Navigate = useNavigate();

  useEffect(() => {
    const fetchClubInfo = async () => {
      setLoading(true);
      setError(null);
      try {
        const response = await Client.get("/club/profile");
        if (response.status === 204) {
          Navigate("edit?edit=0");
          return;
        }
        if (!response.data.success) {
          toast.error(response.data.message);
          setError(response.data.message);
          return;
        }
        setClubInfo(response.data.data);
        setLoading(false);
      } catch (error) {
        console.error("Error fetching club info:", error);

        const errorMessage =
          error.response?.data?.error || "An unknown error occurred";
        setError(errorMessage);
        setLoading(false);
        toast.error(
          `Failed to fetch club information: ${errorMessage}. Please try again later.`
        );
      }
    };

    fetchClubInfo();
  }, [Navigate]);

  const handleRemoveProfile = async()=>{
    try {
      const response = await Client.delete("/club/profile/remove-profile-image");
      if (response.data.success) {
        toast.success("Profile image removed successfully");
        setClubInfo((prev) => ({
          ...prev,
          profileUrl: null,
        }));
      } else {
        toast.error(response.data.message || "Failed to remove profile image");
      }
    } catch (error) {
      console.error("Error removing profile image:", error);
      toast.error("Failed to remove profile image. Please try again later.");
    }

  }

  // Format club data for display in the detail table
  const formattedClubData = formatClubData(clubInfo);

  // const handleSkipFieldChange = (event) => {
  //   setSkipFields(event.target.value.split(",").map((field) => field.trim()));
  // };

  return (
    <Container maxWidth="xl" sx={{ py: 4, pt: 2 }}>
      <BackButton  to={"/dashboard"}/>
      <Paper
        elevation={1}
        sx={{
          bgcolor: "background.default",
          minHeight: "80vh",
          borderRadius: 2,
          overflow: "hidden",
        }}
      >
        {error ? (
          <Typography
            variant="h6"
            color="error"
            sx={{ p: 2, textAlign: "center" }}
          >
            Something went Wrong.
          </Typography>
        ) : (
          <>
            {/* Header */}
            <Box
              sx={{
                display: "flex",
                alignItems: "center",
                justifyContent: "space-between",
                px: "12vw",
                py: 2,
                borderBottom: "1px solid #f0f0f0",
              }}
            >
              <Box sx={{ display: "flex", alignItems: "center", gap: 2 }}>
                <Stack>
                  {!loading ? (
                    <>
                      <Typography
                        variant="h4"
                        component="h4"
                        fontWeight="500"
                        sx={{ textDecoration: "underline" }}
                      >
                        Club Profile
                      </Typography>
                    </>
                  ) : (
                    <>
                      <Skeleton variant="text" width={200} height={32} />
                    </>
                  )}
                </Stack>
              </Box>
              {loading ? (
                <Skeleton variant="circular" width={60} height={60} />
              ) : (
                <Box sx={{ position: "relative", display: "inline-block" }}>
                  <Avatar
                    src={clubInfo?.profileUrl}
                    sx={{
                      width: 100,
                      height: 100,
                      bgcolor: "#f5f5f5",
                      color: "#000",
                    }}
                  >
                    {!clubInfo?.profileUrl && <Person sx={{ fontSize: 60 }} />}
                  </Avatar>

                  {clubInfo?.profileUrl && (
                    <Tooltip title="Remove profile image" placement="top">
                      <IconButton
                        size="small"
                        onClick={handleRemoveProfile} // define this function in your component
                        sx={{
                          position: "absolute",
                          top: 4,
                          right: 4,
                          backgroundColor: "#fff",
                          color: "red",
                          p: "2px",
                          "&:hover": {
                            backgroundColor: "#eee",
                          },
                        }}
                      >
                        <Cancel fontSize="small" />
                      </IconButton>
                    </Tooltip>
                  )}
                </Box>
              )}
            </Box>

            {/* Club Information */}
            <Box>
              {loading ? (
                // Loading skeleton
                Array(6)
                  .fill(0)
                  .map((_, index) => (
                    <Box key={index} sx={{ mb: 2 }}>
                      <Skeleton variant="rectangular" height={40} />
                    </Box>
                  ))
              ) : error ? (
                // Error message
                <Typography color="error" variant="h6" align="center">
                  {error}
                </Typography>
              ) : (
                // Club details table
                <DetailTable
                  details={formattedClubData.details}
                  rowColor={{ odd: "#D4B8A226", even: "#CCF7E840" }}
                />
              )}
            </Box>
            <Box
              sx={{
                p: 2,
                textAlign: "center",
                display: "flex",
                justifyContent: "center",
                gap: 2,
              }}
            >
              {clubInfo && clubInfo.locationUrl && (
                <Box>
                  <Button
                    variant="contained"
                    component="a"
                    href={clubInfo.locationUrl}
                    startIcon={<PinDrop />}
                    sx={{
                      bgcolor: "hsla(132, 56%, 36%, 1) ",
                      fontSize: "16px",
                      ":hover": { bgcolor: "rgb(33, 107, 48)" },
                    }}
                  >
                    Location
                  </Button>
                </Box>
              )}
              <Box>
                <Button
                  variant="contained"
                  sx={{
                    bgcolor: "hsla(242, 56%, 36%, 1) ",
                    fontSize: "16px",
                    ":hover": { bgcolor: "rgb(37, 34, 110)" },
                  }}
                  onClick={() => {
                    Navigate("edit?edit=1");
                  }}
                >
                  Edit Profile
                </Button>
              </Box>
              {/* <Box>
                <Button
                  variant="contained"
                  startIcon={<Add />}
                  sx={{
                    bgcolor: "hsla(132, 56%, 36%, 1)",
                    fontSize: "16px",
                    ":hover": { bgcolor: "rgb(33, 107, 48)" },
                  }}

                >
                  Add Branch
                </Button>
              </Box> */}
            </Box>
          </>
        )}
      </Paper>
    </Container>
  );
};

export default ClubProfilePage;
