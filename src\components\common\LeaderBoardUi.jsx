import {
  Box,
  CircularProgress,
  ToggleButton,
  ToggleButtonGroup,
  Typography,
} from "@mui/material";
import React, { useEffect, useState } from "react";
import { useParams } from "react-router-dom";

import DynamicTable from "../../components/common/DynamicTable";
import { Client } from "../../api/client";
import PlayerSearch from "../../pages/tournament/PlayerSearch";

import UseToast from "../../lib/hooks/UseToast";
import UseGlobalContext from "../../lib/hooks/UseGlobalContext";

const LeaderBoardUi = ({ loading, setLoading, tournamentDetails, currentRound }) => {
  const { title: id } = useParams();
  const { user } = UseGlobalContext()
  const [page, setPage] = useState(1);

  const [round, setRound] = useState();

  const totalRound = tournamentDetails?.numberOfRounds;
  const [totalPages, setTotalPages] = useState(1);

  // Initial state with empty values
  const [search, setSearch] = useState({
    playerName: "",
    ageCategory: "",
    genderCategory: "",
  });

  // Update search state when tournamentDetails changes
  useEffect(() => {
    if (!tournamentDetails ||
      (!tournamentDetails.maleAgeCategory?.length && !tournamentDetails.femaleAgeCategory?.length)) {
      return; // Don't update if no valid tournament details
    }

    // Determine gender category based on tournament category
    let genderCategory = "";
    if (tournamentDetails.tournamentCategory === "open") {
      genderCategory = "male";
    } else {
      genderCategory = tournamentDetails.tournamentCategory;
    }

    // Determine age category based on gender
    let ageCategory = "";
    if (genderCategory === "male" && tournamentDetails.maleAgeCategory?.length > 0) {
      ageCategory = tournamentDetails.maleAgeCategory[0];
    } else if (genderCategory === "female" && tournamentDetails.femaleAgeCategory?.length > 0) {
      ageCategory = tournamentDetails.femaleAgeCategory[0];
    }

    // Update search state with new values
    setSearch({
      playerName: "",
      ageCategory,
      genderCategory,
    });
  }, [tournamentDetails]);

  const [data, setData] = useState([]);

  const getRanking = async () => {
    // Validate required search parameters
    if (!search.ageCategory || !search.genderCategory) {
      // Use toast notification if available
      if (typeof toast !== 'undefined') {
        toast.warning("Please select both age and gender category");
      } else {
        console.warn("Please select both age and gender category");
      }
      setLoading(false);
      return;
    }

    setLoading(true);
    try {
      const params = {
        page: page,
        round: round,
      };
      if (search.playerName) {
        params.playerName = search.playerName;
      }
      if (search.ageCategory) {
        params.ageCategory = search.ageCategory;
      }
      if (search.genderCategory) {
        params.genderCategory = search.genderCategory;
      }
      const response = await Client.get(`ranking-import/${id}`, {
        params,
      });
      if (!response.data.success) return;
      if (response.data?.data?.length === 0) {
        setData([]);
        setPage(1);
        setTotalPages(0);
        return;
      }
      if (response.data.success) {
        const data = response.data.data
        const formattedData = data.map((row) => ({
          ...row,
          ...row.tie_breakers, // flatten tie_breakers (TB1, TB2, TB3, ...)
        }));
        setData(formattedData);
        setTotalPages(response.data.Pagination?.totalPages);
        if (response.data.Pagination?.currentPage !== page) {
          setPage(response.data.Pagination?.currentPage);
        }
      }
    } catch (error) {
      console.error("Error fetching tournament details:", error);
    } finally {
      setLoading(false);
    }
  };

  // Import toast if not already imported
  const { toast } = UseToast();

  useEffect(() => {
    getRanking();
  }, [page, round, search.ageCategory, search.genderCategory, search.playerName]);

  useEffect(() => {
    setRound(currentRound || 0)
  }, [currentRound]);

  const columns = [
    { id: "rank", label: "Rank", align: "center" },
    { id: "total_points", label: "Total Points", align: "center" },
    { id: "player_name", label: "Player Name" },
    { id: "fide_rating", label: "Rating", align: "center" },
    { id: "state", label: "State" },
    { id: "association", label: "Association" },
    { id: "TB1", label: "TB1", align: "center" },
    { id: "TB2", label: "TB2", align: "center" },
    { id: "TB3", label: "TB3", align: "center" },
    { id: "TB4", label: "TB4", align: "center" },
    { id: "TB5", label: "TB5", align: "center" },
    { id: "TB6", label: "TB6", align: "center" },
    { id: "TB7", label: "TB7", align: "center" },
  ];

  // Reset search form while maintaining age and gender category
  const handleReset = React.useCallback(() => {
    setSearch(prevSearch => ({
      playerName: "",
      ageCategory: prevSearch.ageCategory,
      genderCategory: prevSearch.genderCategory,
    }));
  }, []);

  const handleSearch = React.useCallback(async () => {
    setPage(1);

    // Log search parameters to verify they're correct
    console.log("Search parameters:", search);

    // Make sure we're using the latest search state
    await getRanking();
  }, [search]); // Add search as a dependency
  return (
    <>
      <PlayerSearch
        tournamentDetails={tournamentDetails}
        loading={loading}
        search={search}
        setSearch={setSearch}
        handleReset={handleReset}
        handleSearch={handleSearch}
        setLoading={setLoading}
      />
      <Box
        mb={2}
        sx={{
          px: { xs: 1, sm: 2 },
          py: 1,
          borderRadius: 2,
          display: "flex",
          flexDirection: { xs: "column", sm: "row" },
          justifyContent: "space-evenly",
          background: "#A5D8C626",
          alignItems: "center",
          gap: { xs: 1, sm: 0 },
        }}
      >
        <ToggleButton
          value={0}
          selected={round === 0}
          onClick={() => setRound(0)}
          disabled={user.role === 'player' ? currentRound < 1 : false}
          size="small"
          sx={{
            border: "none",
            color:'#000',
            padding: { xs: "4px 8px", sm: "6px 12px" },
            fontSize: { xs: 12, sm: 14 },
            "&.Mui-selected": {
              backgroundColor: "transparent",
              color: "blue",
              fontWeight: "bold",
            },
            "&:hover": {
              backgroundColor: "rgba(0, 0, 255, 0.1)",
            },
          }}
        >
          Starting Rank
        </ToggleButton>

        <Box sx={{ display: "flex", alignItems: "center", flexWrap: "wrap", justifyContent: "center" }}>
          <Typography variant="body1" display="inline" sx={{ fontSize: { xs: 12, sm: 14 }, mr: 1 }}>
            Ranking After:
          </Typography>
          <ToggleButtonGroup
            value={round}
            exclusive
            onChange={(e, value) => {
              if (value !== null) setRound(value);
            }}
            size="small"
            color="primary"
            sx={{
              border: "none",
              "& .MuiButtonBase-root": {
                fontSize: { xs: 12, sm: 14 },
                padding: { xs: "2px 6px", sm: "4px 8px" }
              },
              flexWrap: "wrap",
              justifyContent: "center"
            }}
          >
            {Array.from({ length: totalRound || 0 }, (_, i) => (
              <ToggleButton
                key={i}
                value={i + 1}
                disabled={user.role === 'player' ? i + 1 > currentRound : false}
                sx={{
                  border: "none",
                  '&.Mui-disabled': {
                    border: 'none',
                  },
                  color: '#000',
                  minWidth: { xs: 30, sm: 35 },
                  "&.Mui-selected": {
                    backgroundColor: "transparent",
                    color: "#166DA3",
                    fontWeight: "bold",
                  },
                  "&:hover": {
                    backgroundColor: "rgba(0, 128, 0, 0.1)",
                  },
                }}
              >
                R{i + 1}
              </ToggleButton>
            ))}
          </ToggleButtonGroup>
        </Box>

        <ToggleButton
          value={9999}
          selected={round === 9999}
          disabled={user.role === 'player' ? round < totalRound : false}
          onClick={() => setRound(9999)}
          size="small"
          sx={{
            border: "none",
            padding: { xs: "4px 8px", sm: "6px 12px" },
            fontSize: { xs: 12, sm: 14 },
            "&.Mui-selected": {
              backgroundColor: "transparent",
              color: "green",
              fontWeight: "bold",
            },
            "&:hover": {
              backgroundColor: "rgba(0, 128, 0, 0.1)",
            },
          }}
        >
          Final Ranking List
        </ToggleButton>
      </Box>
      <DynamicTable
        columns={columns}
        data={data}
        loading={false}
        page={page}
        totalPages={totalPages}
        onPageChange={(page) => setPage(page)}
        idField="fideId"
        showDetailsButton={false}
      />
    </>
  );
};

export default LeaderBoardUi;
