import CloseIcon from "@mui/icons-material/Close";
import {
  Box,
  Button,
  IconButton,
  Link,
  Modal,
  Stack,
  TextField,
  Typography,
  Alert,
  InputAdornment,
  CircularProgress,
  FormControlLabel,
  Checkbox,
} from "@mui/material";
import React, { useEffect, useState } from "react";
import backgroundImage from "../../assets/singupbackground.png";
import { z } from "zod";
import { Visibility, VisibilityOff } from "@mui/icons-material";
import { Client } from "../../api/client";
import UseToast from "../../lib/hooks/UseToast";
import useGlobalContext from "../../lib/hooks/UseGlobalContext";
import { useNavigate } from "react-router-dom";

const Login = ({ open, setOpen }) => {
  const toast = UseToast();
  const { updateUser, setToken, broadcastLogin } = useGlobalContext();
  const [formData, setFormData] = useState({
    emailOrMobile: "",
    password: "",
    rememberMe: true,
  });
  const [errors, setErrors] = useState({});
  const [showPassword, setShowPassword] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const navigate = useNavigate();

  // Zod schema for form validation
  const loginSchema = z.object({
    emailOrMobile: z.union(
      [
        z.string().email({ message: "Invalid email format" }),
        z
          .string({ message: "Email or mobile number is required" })
          .regex(/^(91)?\d{10}$/, {
            message: "Invalid mobile number format",
          }),
      ],
      {
        errorMap: () => ({
          message: "Please enter a valid email or 10-digit mobile number",
        }),
      }
    ),
    rememberMe: z.boolean().optional(),
    password: z
      .string({ message: "Password is required" })
      .min(6, { message: "Password must be at least 6 characters long" }),
  });
  useEffect(() => {
    if (open) {
      setFormData({
        emailOrMobile: "",
        password: "",
        rememberMe: true,
      });
    }
  }, [open]);

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData((prevState) => ({
      ...prevState,
      [name]: value,
    }));

    // Clear specific field error when typing
    if (errors[name]) {
      setErrors((prev) => ({ ...prev, [name]: null }));
    }
  };

  const validateForm = () => {
    try {
      loginSchema.parse(formData);
      setErrors({});
      return true;
    } catch (error) {
      const formattedErrors = {};
      error.errors.forEach((err) => {
        const path = err.path[0];
        formattedErrors[path] = err.message;
      });
      setErrors(formattedErrors);
      return false;
    }
  };

  const handleSubmit = async (e) => {
    e.preventDefault();

    if (!validateForm()) return;

    setIsSubmitting(true);

    try {
      // Use POST instead of GET for login requests
      const response = await Client.post("/auth/login", formData);

      if (!response.data.success) {
        toast.error(response.data.message || "Login failed");
        setIsSubmitting(false);
        return;
      }

      // Extract user data and token
      const { user, accessToken } = response.data.data;

      // Fix potential user object structure to match GlobalContext expectations
      const userData = {
        userId: user?.userId,
        name: user.name,
        email: user?.email,
        role: user.role,
      };

      // Update token first to ensure API calls have the token
      setToken(accessToken);

      // Then update the user
      updateUser(userData);

      // Broadcast login to other tabs with the new token
      broadcastLogin(userData, accessToken);

      toast.success("Successfully Logged In");

      // Close login modal before navigation
      setOpen({ ...open, login: false });

      // Reset form data
      setFormData({
        emailOrMobile: "",
        password: "",
        rememberMe: true,
      });
      if (sessionStorage.getItem("new_user") === "true") {
        navigate("/dashboard/profile/edit?edit=0");
        return;
      } else {
        navigate("/dashboard");
      }
    } catch (error) {
      let errorMessage =
        "An unexpected error occurred. Please try again later.";

      if (error.response) {
        // The request was made and the server responded with a status code
        // that falls out of the range of 2xx
        errorMessage =
          error.response.data.message || "Please check your credentials";
      } else if (error.request) {
        // The request was made but no response was received
        errorMessage =
          "No response from server. Please check your internet connection.";
      } else {
        // Something happened in setting up the request that triggered an Error
        errorMessage = error.message;
      }

      toast.error(errorMessage);
      console.error("Login error:", error);
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleClickShowPassword = () => {
    setShowPassword(!showPassword);
  };

  const handleMouseDownPassword = (event) => {
    event.preventDefault();
  };

  return (
    <Modal
      open={open}
      onClose={() => !isSubmitting && setOpen({ ...open, login: false })}
      aria-labelledby="login-modal"
      aria-describedby="login-form"
      sx={{
        display: "flex",
        alignItems: "center",
        justifyContent: "center",
      }}
    >
      <Box
        sx={{
          width: "100%",
          height: "100%",
          bgcolor: "rgba(0, 0, 0, 0.5)",
          backdropFilter: "blur(2px)",
          display: "flex",
          justifyContent: "center",
          alignItems: "center",
          paddingX: "3vw",
        }}
      >
        <Box
          sx={{
            width: 488,
            height: "auto",
            maxHeight: "90vh",
            position: "relative",
            overflow: "auto",
            borderRadius: 2,
            bgcolor: "white",
          }}
        >
          <Box
            sx={{
              width: "100%",
              minHeight: "100%",
              backgroundImage: `url(${backgroundImage})`,
              backgroundSize: "cover",
              position: "relative",
              display: "flex",
              flexDirection: "column",
              alignItems: "center",
              padding: 3,
            }}
          >
            <IconButton
              sx={{
                position: "absolute",
                top: 13,
                right: 21,
                color: "white",
              }}
              onClick={() =>
                !isSubmitting && setOpen({ ...open, login: false })
              }
              disabled={isSubmitting}
            >
              <CloseIcon fontSize="large" />
            </IconButton>

            <Typography
              variant="h4"
              sx={{
                fontFamily: "Prosto One, Helvetica",
                color: "white",
                fontSize: 28,
                mb: 2,
              }}
            >
              ChessBrigade.com
            </Typography>

            <Typography
              variant="h5"
              sx={{
                fontFamily: "Poppins, Helvetica",
                color: "white",
                fontSize: 24,
                mb: 2,
              }}
            >
              Login
            </Typography>

            <form onSubmit={handleSubmit} noValidate>
              <Stack spacing={2} sx={{ width: "100%", maxWidth: 336 }}>
                {/* Email or Mobile field */}
                <TextField
                  name="emailOrMobile"
                  placeholder="Mobile Number / Email ID"
                  value={formData.emailOrMobile}
                  onChange={handleChange}
                  fullWidth
                  variant="outlined"
                  error={!!errors.emailOrMobile}
                  helperText={errors.emailOrMobile}
                  disabled={isSubmitting}
                  sx={{
                    bgcolor: "white",
                    borderRadius: 1,
                    "& .MuiInputBase-input": {
                      padding: "8px 14px",
                    },
                    "& .MuiOutlinedInput-root": {
                      height: 40,
                    },
                    "& .MuiFormHelperText-root": {
                      color: "error.main",
                      backgroundColor: "rgba(255, 255, 255, 0.7)",
                      margin: 0,
                      paddingLeft: 1,
                      borderRadius: "0 0 4px 4px",
                    },
                  }}
                  inputProps={{
                    "aria-label": "Email or Mobile Number",
                  }}
                />

                {/* Password field */}
                <TextField
                  name="password"
                  placeholder="Password"
                  type={showPassword ? "text" : "password"}
                  value={formData.password}
                  onChange={handleChange}
                  fullWidth
                  variant="outlined"
                  error={!!errors.password}
                  helperText={errors.password}
                  disabled={isSubmitting}
                  InputProps={{
                    endAdornment: (
                      <InputAdornment position="end">
                        <IconButton
                          aria-label="toggle password visibility"
                          onClick={handleClickShowPassword}
                          onMouseDown={handleMouseDownPassword}
                          edge="end"
                          size="small"
                        >
                          {showPassword ? <VisibilityOff /> : <Visibility />}
                        </IconButton>
                      </InputAdornment>
                    ),
                  }}
                  sx={{
                    bgcolor: "white",
                    borderRadius: 1,
                    "& .MuiOutlinedInput-root": {
                      height: 40,
                    },
                    "& .MuiInputBase-input": {
                      padding: "8px 14px",
                    },
                    "& .MuiFormHelperText-root": {
                      color: "error.main",
                      backgroundColor: "rgba(255, 255, 255, 0.7)",
                      margin: 0,
                      paddingLeft: 1,
                      borderRadius: "0 0 4px 4px",
                    },
                  }}
                  inputProps={{
                    "aria-label": "Password",
                  }}
                />
                {/* Remember me checkbox */}
                <FormControlLabel
                  sx={{
                    color: "white",
                    fontFamily: "Poppins, Helvetica",
                    fontSize: 16,
                    mt: "0px !important",
                  }}
                  control={
                    <Checkbox
                      checked={formData.rememberMe}
                      onChange={(e) =>
                        setFormData((prev) => ({
                          ...prev,
                          rememberMe: e.target.checked,
                        }))
                      }
                      name="rememberMe"
                      sx={{
                        color: "white",
                        "&.Mui-checked": {
                          color: "white",
                        },
                      }}
                      disabled={isSubmitting}
                    />
                  }
                  label={
                    <Typography
                      sx={{
                        color: "white",
                        fontFamily: "Poppins, Helvetica",
                        fontSize: 16,
                      }}
                    >
                      Remember Me
                    </Typography>
                  }
                />

                {/* Login button */}
                <Button
                  variant="contained"
                  fullWidth
                  type="submit"
                  disabled={isSubmitting}
                  sx={{
                    bgcolor: "white",
                    color: "black",
                    height: 40,
                    borderRadius: 1,
                    textTransform: "none",
                    fontFamily: "Poppins, Helvetica",
                    fontWeight: 500,
                    fontSize: 16,
                    mt: 1,
                    "&:hover": {
                      bgcolor: "rgba(255, 255, 255, 0.8)",
                    },
                    "&:disabled": {
                      bgcolor: "rgba(255, 255, 255, 0.7)",
                    },
                  }}
                >
                  {isSubmitting ? (
                    <CircularProgress size={24} color="inherit" />
                  ) : (
                    "Login"
                  )}
                </Button>
              </Stack>
            </form>

            <Box display="flex" justifyContent="center" mt={2}>
              <Link
                component="button"
                underline="always"
                sx={{
                  color: "white",
                  fontFamily: "Poppins, Helvetica",
                  fontSize: 16,
                }}
                onClick={() =>
                  !isSubmitting &&
                  setOpen({ ...open, login: false, forgotpassword: true })
                }
                disabled={isSubmitting}
              >
                <Typography
                  sx={{
                    color: "rgba(255, 255, 255, 0.8)",
                    fontFamily: "Poppins, Helvetica",
                    fontSize: 16,
                  }}
                >
                  Forgot Password?
                </Typography>
              </Link>
            </Box>
            <Box display="flex" justifyContent="center" mt={2}>
              <Typography
                sx={{
                  color: "rgba(255, 255, 255, 0.8)",
                  fontFamily: "Poppins, Helvetica",
                  fontSize: 16,
                }}
              >
                If you do not have an account?
                <Button
                  component="button"
                  disabled={isSubmitting}
                  sx={{
                    color: "white",
                    fontFamily: "Poppins, Helvetica",
                    fontSize: 16,
                    textTransform: "none",
                  }}
                  onClick={() =>
                    !isSubmitting &&
                    setOpen({ ...open, login: false, signup: true })
                  }
                >
                  Sign Up
                </Button>
              </Typography>
            </Box>
          </Box>
        </Box>
      </Box>
    </Modal>
  );
};

export default Login;
