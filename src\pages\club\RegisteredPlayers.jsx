import { Box, Button, Container } from "@mui/material";
import React, { useEffect, useState } from "react";
import DynamicTable from "../../components/common/DynamicTable";
import BackButton from "../../components/common/BackButton";
import PlayerSearchForm from "../../components/registration/PlayerSearchForm";
import { useParams } from "react-router-dom";
import { Client } from "../../api/client";
import UseToast from "../../lib/hooks/UseToast";

const RegisterPlayers = () => {
  const { title } = useParams();
  const toast = UseToast();
  const limit = 10;

  const [page, setPage] = useState(1);
  const [tournamentDetails, setTournamentDetails] = useState([]);
  const [loading, setLoading] = useState(false);
  const [search, setSearch] = useState({
    playerName: "",
    playerId: "",
    ageCategory: "",
    genderCategory: "",
    registerId:"",
  });
  const [players, setPlayers] = useState([]);
  const [total, setTotal] = useState(0);
  const [totalPages, setTotalPages] = useState(0);
  useEffect(() => {
    const fetchTournamentDetails = async () => {
      setLoading(true);
      try {
        const response = await Client.get(`/tournament/${title}`);
        if (response.data.success) {
          setTournamentDetails(response.data.data);
        } else {
          toast.error(
            response.data.message || "Failed to fetch tournament details"
          );
          // navigate("/tournaments");
        }
      } catch (error) {
        console.error("Error fetching tournament details:", error);
        toast.error("An error occurred while fetching tournament details");
        // navigate("/tournaments");
      } finally {
        setLoading(false);
      }
    };

    fetchTournamentDetails();
  }, [title]);
  const columns = [
    {
      id: "sno",
      label: "S.No",
      align: "center",
      format: (_, item, index) => (page - 1) * limit + index + 1,
    },
    { id: "playerTitle", label: "Title" },
    { id: "playerName", label: "Name" },
    { id: "fideRating", label: "FIDE Rating", align: "center" },
    { id: "fideId", label: "FIDE ID" },
    { id: "aicfId", label: "National ID" },
    { id: "stateId", label: "State ID" },
    { id: "districtId", label: "District ID" },
    { id: "club", label: "club" },
  ];
  // Reset search form
  const handleReset = () => {
    setSearch({
      playerName: "",
      playerId: "",
      ageCategory: "",
      genderCategory: "",
    });
  };

  const fetchPlayers = async () => {
    setLoading(true);
    try {
      const params = {
        playerName: search.playerName,
        playerId: search.playerId,
        ageCategory: search.ageCategory,
        genderCategory: search.genderCategory,
        registerId: search.registerId,
        page: page,
        limit: 10,
        tournamentId: title,
      };
      const response = await Client.get(`club/tournament/${title}/players`, {
        params,
      });
      if (!response.data.success) {
        toast.info(response.data.error.massage);
        return;
      }
      if (response.data.status === 204) {
        toast.info("No Player found");
        setPlayers([]);
        setPage(0);
        setTotal(0);
        setTotalPages(0);
      }
      const data = response.data?.data;
      setPlayers(data?.players);
      setTotalPages(data?.totalPages);
      setPage(data?.currentPage);
      setTotal(data?.total);
    } catch (error) {
      console.error("Error searching players:", error);
      toast.error("An error occurred while searching for players");
    } finally {
      setLoading(false);
    }
  };

  const handleSearch = async () => {
    fetchPlayers();
  };
  console.log('search', search)
  const fetchPlayersReport = async () => {
    // return;
    // setLoading(true);

    try {

      const response = await Client.get("/report/players-tournament", {
        params: {
          genderCategory: search.genderCategory || '',
          ageCategory: search.ageCategory || 'OPEN',
          tournamentTitle: title
        },
        responseType: 'blob',
      });

      // if (!response.data) {
      //   toast.error(response.error || "Failed to export player details"
      //   );
      //   return;
      // }
      const url = window.URL.createObjectURL(new Blob([response.data]));
      console.log("url", url)
      const link = document.createElement('a');
      link.href = url;
      link.setAttribute('download', 'Players_report.xlsx');
      document.body.appendChild(link);
      link.click();
      link.remove();
    } catch (error) {
      console.error("Download failed:", error);
      toast.error(error || "Failed to export player details")
    }
  };

  return (
    <Container maxWidth={"xl"} sx={{ minHeight: "70dvh", my: 4, mt: 2 }}>
      <BackButton />

      <PlayerSearchForm
        tournamentDetails={tournamentDetails}
        loading={loading}
        search={search}
        setSearch={setSearch}
        handleReset={handleReset}
        handleSearch={handleSearch}
        setLoading={setLoading}
      />
      <DynamicTable
        columns={columns}
        data={players}
        loading={loading}
        page={page}
        detailsPath="/players/"
        totalPages={totalPages}
        onPageChange={(page) => setPage(page)}
        idField="cbid"
        showDetailsButton={false}
      />

      <Box mt={5} sx={{ display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
        <Button size="small" variant="contained" onClick={fetchPlayersReport}>Download Report</Button>
      </Box>


    </Container>
  );
};

export default RegisterPlayers;
