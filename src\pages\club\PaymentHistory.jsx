import React, { useState, useEffect } from "react";
import {
  Box,
  Button,
  Container,
  Paper,
  Typography,
  Grid,
  TextField,
  Chip,
  MenuItem,
  IconButton,
} from "@mui/material";

import { Client } from "../../api/client";
import UseToast from "../../lib/hooks/UseToast";
import { AxiosError } from "axios";
import { Link } from "react-router-dom";
import SearchIcon from "@mui/icons-material/Search";
import { RestartAlt } from "@mui/icons-material";
import FileDownloadIcon from "@mui/icons-material/FileDownload";
import DynamicTable from "../../components/common/DynamicTable";
import BackButton from "../../components/common/BackButton";

const ClubPaymentHistoryPage = () => {
  const [payments, setPayments] = useState([]);
  const [loading, setLoading] = useState(false);
  const [totalPages, setTotalPages] = useState(0);
  const [totalRecords, setTotalRecords] = useState(0);

  // States for form inputs
  const [search, setSearch] = useState({
    transactionId: "",
    tournamentTitle: "",
    status: "all",
  });
  const [page, setPage] = useState(1);
  const limit = 10; // Adjust as needed
  const toast = UseToast();

  // Handler functions
  const handlePageChange = (_, value) => {
    setPage(value);
    fetchPayments(value);
  };

  const handleReset = () => {
    setSearch({
      transactionId: "",
      tournamentTitle: "",
      status: "all",
    });
    fetchPayments(1);
  };
  const handleSearch = () => {
    fetchPayments(1);
  };

  const fetchPayments = async (pageNumber) => {
    setLoading(true);
    try {
      const params = {
        page: pageNumber,
        limit,
      };

      if (search.transactionId !== "") {
        params.transactionId = search.transactionId;
      }
      if (search.tournamentTitle !== "") {
        params.tournamentTitle = search.tournamentTitle;
      }
      if (search.status !== "all") {
        params.status = search.status;
      }

      const response = await Client.get("/payment/club", { params });

      if (response.status === 204) {
        toast.info("No payment records found");
        setPayments([]);
        setTotalRecords(0);
        setTotalPages(0);
        setPage(1);
        return;
      }

      if (!response.data.success) {
        toast.info("No payment records found");
        return;
      }

      // Check if we have bulk registrations data
      if (
        response.data.data.bulkRegistrations &&
        response.data.data.bulkRegistrations.length > 0
      ) {
        // Process bulk registration payments
        setPayments(
          response.data.data.bulkRegistrations.map((br) => ({
            ...br.payment,
            bulkRegistrationId: br.bulkRegistrationId,
            playersCount: br.playersCount || 0,
            registrationStatus: br.registrationStatus,
            tournament: br.tournament,
            isBulkRegistration: true,
          }))
        );
      } else {
        // Regular payments
        setPayments(response.data.data.payments || []);
      }

      setTotalRecords(response.data.data.total);
      const totalPage = Math.ceil(response.data.data.total / limit);
      setTotalPages(totalPage);
    } catch (error) {
      if (error instanceof AxiosError && error.response) {
        toast.info(error.response.data.error);
        return;
      }
      console.error("Error fetching payment history:", error);
      toast.info("An error occurred. Please try again later.");
    } finally {
      setLoading(false);
    }
  };

  // Format date for display
  const formatDate = (dateString) => {
    if (!dateString) return "N/A";
    const date = new Date(dateString);
    if (isNaN(date.getTime())) return dateString;
    const options = {
      day: "2-digit",
      month: "short",
      year: "numeric",
      hour: "2-digit",
      minute: "2-digit",
      hour12: true,
    };
    return date.toLocaleString("en-US", options);
  };

  // Get status chip color based on payment status
  const getStatusColor = (status) => {
    switch (status?.toLowerCase()) {
      case "paid":
      case "success":
        return "success";
      case "pending":
        return "warning";
      case "failed":
        return "error";
      default:
        return "default";
    }
  };

  // Initial fetch of payments
  useEffect(() => {
    fetchPayments(1);
  }, []);

  const HandleReceipt = async (txnId) => {
    try {
      const { generatePdfAndDownload } = await import(
        "../../components/common/PaymentReceiptTemplate"
      );

      await generatePdfAndDownload({ toast, txnId });
    } catch (error) {
      if (error instanceof AxiosError && error.response) {
        toast.info(error.response.data.error);
        return;
      }
      console.error("Receipt generation failed:", error);
      toast.error("Failed to generate receipt PDF");
    }
  };

  return (
    <Container
      maxWidth="xl"
      sx={{
        py: 4,
        pb: 8,
        minHeight: "0vh",
        display: "flex",
        flexDirection: "column",
      }}
    >
      <BackButton />
      {/* Search Form */}
      <Paper
        sx={{ mb: 3, p: 3, bgcolor: "#f9f9f9", borderRadius: 2, boxShadow: 3 }}
      >
        <Typography
          variant="h5"
          gutterBottom
          sx={{ mb: 2, fontWeight: "bold", color: "#3f51b5" }}
        >
          Payment History
        </Typography>
        <Grid container spacing={3} alignItems="center">
          <Grid item xs={12} sm={6} md={3}>
            <TextField
              variant="outlined"
              fullWidth
              value={search.transactionId}
              onChange={(e) =>
                setSearch({ ...search, transactionId: e.target.value })
              }
              sx={{ bgcolor: "white" }}
              placeholder="Enter transaction ID"
            />
          </Grid>

          <Grid item xs={12} sm={6} md={3}>
            <TextField
              variant="outlined"
              fullWidth
              value={search.tournamentTitle}
              onChange={(e) =>
                setSearch({ ...search, tournamentTitle: e.target.value })
              }
              sx={{ bgcolor: "white" }}
              placeholder="Enter tournament title"
            />
          </Grid>
          <Grid item xs={12} sm={6} md={3}>
            <TextField
              select
              variant="outlined"
              fullWidth
              placeholder="Select status"
              value={search.status}
              onChange={(e) => setSearch({ ...search, status: e.target.value })}
              sx={{ bgcolor: "white" }}
            >
              <MenuItem value="all">All</MenuItem>
              <MenuItem value="paid">Paid</MenuItem>
              <MenuItem value="pending">Pending</MenuItem>
              <MenuItem value="failed">Failed</MenuItem>
            </TextField>
          </Grid>

          <Grid
            item
            xs={12}
            sm={6}
            md={3}
            sx={{ display: "flex", gap: 1, mt: { xs: 2, md: 0 } }}
          >
            <Button
              variant="containedSecondary"
              color="secondary"
              sx={{
                width: "40px",
              }}
              onClick={handleReset}
              disabled={loading}
            >
              <RestartAlt />
            </Button>
            <Button
              variant="contained"
              color="primary"
              fullWidth
              onClick={handleSearch}
              startIcon={<SearchIcon />}
              sx={{
                bgcolor: "#3f51b5",
                textTransform: "none",
                height: "56px",
                fontSize: "16px",
                fontWeight: "bold",
              }}
            >
              Search
            </Button>
          </Grid>
        </Grid>
      </Paper>

      {/* Payment History Table */}
      <DynamicTable
        columns={[
          {
            id: "transactionId",
            label: "Transaction ID",
            format: (_, payment) => payment.paymentTransactionId || "N/A",
          },
          {
            id: "date",
            label: "Date",
            format: (_, payment) => formatDate(payment.paymentDate),
          },
          {
            id: "amount",
            label: "Amount",
            format: (_, payment) => (
              <Typography variant="body1" fontWeight="medium">
                {payment.paymentCurrency || "INR"}{" "}
                {payment.paymentAmount || "0.00"}
              </Typography>
            ),
          },
          {
            id: "paymentMethod",
            label: "Method",
            format: (_, payment) => payment.paymentMode || "Online",
          },
          {
            id: "status",
            label: "Status",
            format: (_, payment) => (
              <Chip
                label={payment.paymentStatus || "Unknown"}
                color={getStatusColor(payment.paymentStatus)}
                size="small"
                variant="filled"
              />
            ),
          },
          {
            id: "type",
            label: "Type",
            format: (_, payment) => (
              <Chip
                label={
                  payment.isBulkRegistration
                    ? "Bulk Registration"
                    : "Individual"
                }
                color={payment.isBulkRegistration ? "primary" : "default"}
                size="small"
                variant="filled"
              />
            ),
          },
          {
            id: "playersCount",
            label: "Players",
            format: (_, payment) => (
              <Chip
                label={payment.playersCount || "N/A"}
                color={"primary"}
                size="small"
                variant="filled"
              />
            ),
          },

          {
            id: "tournament",
            label: "Tournament",
            format: (_, payment) =>
              payment?.tournament?.title ? (
                <Link
                  to={`/tournaments/${payment?.tournament?.title}`}
                  style={{ textDecoration: "none" }}
                >
                  <Typography
                    variant="body1"
                    color="primary"
                    sx={{
                      textTransform: "capitalize",
                      fontWeight: "medium",
                      textWrap: "balance",
                    }}
                  >
                    {payment?.tournament?.title
                      .toLowerCase()
                      .replace(/-/g, " ")}
                  </Typography>
                </Link>
              ) : (
                "N/A"
              ),
          },

          {
            id: "report",
            label: "Receipt",
            format: (_, payment) =>
              payment?.paymentStatus === "paid" ? (
                <IconButton>
                  <FileDownloadIcon
                    onClick={() => HandleReceipt(payment.paymentTransactionId)}
                  />
                </IconButton>
              ) : (
                "N/A"
              ),
          },
          {
            id: "actions",
            label: "Actions",
            format: (_, payment) =>
              payment.isBulkRegistration &&
              payment.playersCount > 0 &&
              payment.registrationStatus !== "pending" &&
              payment.paymentStatus !== "failed" &&
              payment.paymentStatus !== "pending" ? (
                <Button
                  variant="outlined"
                  size="small"
                  component={Link}
                  to={`/dashboard/tournaments/${payment?.tournament?.title}/bulk-registration/register/${payment.bulkRegistrationId}?show=true`}
                  sx={{ textTransform: "none" }}
                >
                  View Players
                </Button>
              ) : null,
          },
        ]}
        data={payments}
        loading={loading}
        page={page}
        totalPages={totalPages}
        onPageChange={handlePageChange}
        detailsPath=""
        idField="id"
        showDetailsButton={false}
        tableContainerProps={{
          sx: {
            minHeight: "400px",
            maxHeight: "600px",
          },
        }}
      />

      {/* Record Count */}
      <Box
        sx={{
          display: "flex",
          justifyContent: "space-between",
          alignItems: "center",
          mt: 2,
          p: 2,
          bgcolor: "#fafafa",
          borderRadius: 1,
        }}
      >
        <Typography variant="h6" color="text.primary">
          Showing {payments.length} of {totalRecords} records
        </Typography>
      </Box>
    </Container>
  );
};

export default ClubPaymentHistoryPage;
