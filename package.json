{"name": "chessbrigade-ui", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.0", "@hookform/resolvers": "^5.0.0", "@mui/icons-material": "^6.4.7", "@mui/material": "^6.4.7", "@react-google-maps/api": "^2.20.6", "@react-pdf/renderer": "^4.3.0", "axios": "^1.8.3", "browser-image-compression": "^2.0.2", "file-saver": "^2.0.5", "lucide-react": "^0.511.0", "react": "^19.0.0", "react-dom": "^19.0.0", "react-hook-form": "^7.55.0", "react-router-dom": "^7.3.0", "zod": "^3.24.2"}, "devDependencies": {"@eslint/js": "^9.21.0", "@types/react": "^19.0.10", "@types/react-dom": "^19.0.4", "@vitejs/plugin-react": "^4.3.4", "eslint": "^9.21.0", "eslint-plugin-react-hooks": "^5.1.0", "eslint-plugin-react-refresh": "^0.4.19", "globals": "^15.15.0", "vite": "^6.2.0"}}