import {
  <PERSON>,
  <PERSON><PERSON>,
  CircularP<PERSON>ress,
  Container,
  Toggle<PERSON>utton,
  ToggleButtonGroup,
  Typography,
} from "@mui/material";
import React, { useEffect, useState } from "react";

import DynamicTable from "../../components/common/DynamicTable";
import { useParams } from "react-router-dom";
import { Client } from "../../api/client";
import PlayerSearch from "../../pages/tournament/PlayerSearch";

import UseToast from "../../lib/hooks/UseToast";
import UseGlobalContext from "../../lib/hooks/UseGlobalContext";

const PairingDetailsUi = ({ tournamentDetails, loading, setLoading, currentRound }) => {
  const { title: id } = useParams();
  const [page, setPage] = useState(1);
  const { user } = UseGlobalContext();

  const [totalPages, setTotalPages] = useState(0);

  const totalRound = tournamentDetails?.numberOfRounds;

  const [round, setRound] = useState(1);

  const toast = UseToast();
  const [search, setSearch] = useState({
    playerName: "",
    ageCategory: "",
    genderCategory: "",
  });

  const [data, setData] = useState([]);

  const fetchPairingDetails = async () => {
    setLoading(true);
    try {
      const params = {
        page: page,
        round: round,
      };
      if (search.playerName) {
        params.playerName = search.playerName;
      }
      if (search.ageCategory) {
        params.ageCategory = search.ageCategory;
      }
      if (search.genderCategory) {
        params.genderCategory = search.genderCategory;
      }
      const response = await Client.get(`pairing-import/${id}`, {
        params,
      });
      if (!response.data.success) return;
      if (response.data?.data?.length === 0) {
        setData([]);
        setPage(1);
        setTotalPages(0);

        return;
      }
      setData(response.data.data);
      setTotalPages(response.data.Pagination?.totalPages);
      if (response.data.Pagination?.currentPage !== page) {
        setPage(response.data.Pagination?.currentPage);
      }

      setLoading(false);
    } catch (error) {
      console.error("Error fetching tournament details:", error);
      toast.error("An error occurred while fetching tournament details");
      // navigate("/tournaments");
    }
    finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchPairingDetails();
  }, [page, round, search.ageCategory, search.genderCategory]);

  const columns = [
    { id: "board_no", label: "Board No", align: "center" },
    { id: "white_player_ranking", label: "Rank", align: "center" },
    { id: "white_player_fide_rating", label: "FIDE Rating", align: "center" },
    { id: "white_player_points", label: "Points", align: "center" },
    { id: "white_player_name", label: "White", align: "left" },
    { id: "result", label: "Result", align: "center" },
    { id: "black_player_name", label: "Black", align: "left" },
    { id: "black_player_points", label: "Points", align: "center" },
    { id: "black_player_fide_rating", label: "FIDE Rating", align: "center" },
    { id: "black_player_ranking", label: "Rank", align: "center" },
  ];

  // Reset search form
  const handleReset = React.useCallback(() => {
    setSearch({
      playerName: "",
      ageCategory: "",
      genderCategory: "",
    });
    fetchPairingDetails();
  }, []);
  const handleSearch = async () => {
    if (!search.playerName && !search.ageCategory && !search.genderCategory) {
      return;
    }

    setLoading(true);

    fetchPairingDetails(1);
  };

  
  useEffect(() => {
    setRound(currentRound || 0);
  }, [currentRound]);

  return (
    <>
      <Box
        sx={{
          my: 2,
          p: 2,
          borderRadius: 2,
          display: "flex",
          flexDirection: "row",
          justifyContent: "flex-start",
          background: "#A5D8C626",
          alignItems: "center",
          gap: 50,
        }}
      >
        <Typography
          variant="body1"
          color="#000"
          gutterBottom
          sx={{ fontWeight: "500" }}
        >
          Pairing Details
        </Typography>

        <Box>
          <Typography variant="body1" display="inline">
            Round &nbsp;
          </Typography>
          <ToggleButtonGroup
            value={round}
            exclusive
            
            onChange={(e, value) => {
              if (value !== null) setRound(value);
            }}
            size="small"
            color="primary"
            sx={{
              border: "none",
              fontSize: 16,
              color:'#000',
              "& .MuiButtonBase-root": { fontSize: "1rem" },
              '&.Mui-disabled': {border: 'none'},
            }}
          >
            {Array.from({ length: totalRound || 0 }, (_, i) => (
              <ToggleButton
                key={i}
                value={i + 1}
                disabled={ !user || user?.role === 'player' ? i + 1 > currentRound : false}
                sx={{
                  border: "none",
                   '&.Mui-disabled': {border: 'none'},
                  "&.Mui-selected": {
                    backgroundColor: "transparent",
                    color: "#166DA3",
                    fontWeight: "bold",
                  },
                  "&:hover": {
                    backgroundColor: "rgba(0, 128, 0, 0.1)",
                  },
                }}
              >
                {i + 1}
              </ToggleButton>
            ))}
          </ToggleButtonGroup>
        </Box>
      </Box>
      <PlayerSearch
        tournamentDetails={tournamentDetails}
        loading={loading}
        search={search}
        setSearch={setSearch}
        handleReset={handleReset}
        handleSearch={handleSearch}
        setLoading={setLoading}
      />

      <DynamicTable
        columns={columns}
        data={data}
        loading={false}
        page={page}
        totalPages={totalPages}
        onPageChange={(page) => setPage(page)}
        idField="fideId"
        showDetailsButton={false}
      />
      
    </>
  );
};

export default PairingDetailsUi;
